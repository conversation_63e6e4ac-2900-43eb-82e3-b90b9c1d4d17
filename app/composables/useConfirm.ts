import { createSharedComposable } from '@vueuse/core'

export interface ConfirmOptions {
  title: string
  description?: string
  icon?: string
  confirmText?: string
  cancelText?: string
  onConfirm: () => void | Promise<void>
}

const _useConfirm = () => {
  const isOpen = ref(false)
  const options = ref<ConfirmOptions | null>(null)

  const openConfirm = (confirmOptions: ConfirmOptions) => {
    options.value = confirmOptions
    isOpen.value = true
  }

  const closeConfirm = () => {
    isOpen.value = false
    options.value = null
  }

  const handleConfirm = async () => {
    if (options.value?.onConfirm) {
      await options.value.onConfirm()
    }
    closeConfirm()
  }

  const handleCancel = () => {
    closeConfirm()
  }

  return {
    isOpen,
    options,
    openConfirm,
    closeConfirm,
    handleConfirm,
    handleCancel
  }
}

export const useConfirm = createSharedComposable(_useConfirm)
